{"name": "callsaver4", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio"}, "dependencies": {"@headlessui/react": "^2.2.1", "@heroicons/react": "^2.1.1", "@prisma/client": "^5.10.2", "@radix-ui/react-select": "^2.1.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.0.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.3", "@tanstack/react-query": "^5.74.3", "@tanstack/react-query-devtools": "^5.74.3", "@vercel/analytics": "^1.5.0", "@vonage/server-sdk": "^3.20.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "^10.18.0", "geoip-lite": "^1.4.10", "lottie-react": "^2.4.1", "lucide-react": "^0.323.0", "next": "^15.3.5", "next-auth": "^4.24.5", "next-intl": "^4.0.2", "node-fetch": "^3.3.2", "openai": "^4.93.0", "postgres": "^3.4.5", "react": "^19.1.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.1", "sharp": "^0.33.5", "stripe": "^12.18.0", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "twilio": "^5.5.2", "web-vitals": "^3.5.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@next/bundle-analyzer": "^15.3.5", "@types/node": "22.14.0", "@types/react": "19.0.12", "autoprefixer": "^10.4.16", "compression-webpack-plugin": "^11.1.0", "eslint": "^8.56.0", "eslint-config-next": "^15.3.5", "postcss": "^8.4.32", "prisma": "^5.10.2", "tailwindcss": "^3.3.6", "typescript": "5.8.3"}}