const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function testPrismaConnection() {
  console.log('🔍 Testing Prisma connection to Supabase database...');
  
  try {
    // Test basic connection
    console.log('1. Testing database connection...');
    await prisma.$connect();
    console.log('✅ Successfully connected to database');

    // Test table access
    console.log('2. Testing table access...');
    const userCount = await prisma.user.count();
    console.log(`✅ Users table accessible - Count: ${userCount}`);

    const phoneNumberCount = await prisma.phoneNumber.count();
    console.log(`✅ Phone numbers table accessible - Count: ${phoneNumberCount}`);

    const callCount = await prisma.call.count();
    console.log(`✅ Calls table accessible - Count: ${callCount}`);

    const messageCount = await prisma.message.count();
    console.log(`✅ Messages table accessible - Count: ${messageCount}`);

    // Test creating a test user (will be rolled back)
    console.log('3. Testing CRUD operations...');
    const testUser = await prisma.user.create({
      data: {
        id: 'test-user-' + Date.now(),
        email: '<EMAIL>',
        name: 'Test User',
        timezone: 'UTC',
        preferences: {}
      }
    });
    console.log('✅ User creation successful:', testUser.email);

    // Clean up test user
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    console.log('✅ User deletion successful');

    // Test RLS policies (should work with service role)
    console.log('4. Testing Row Level Security...');
    const users = await prisma.user.findMany({
      take: 5
    });
    console.log(`✅ RLS test passed - Retrieved ${users.length} users`);

    console.log('\n🎉 All Prisma connection tests passed!');
    console.log('✅ Database connection: Working');
    console.log('✅ Table access: Working');
    console.log('✅ CRUD operations: Working');
    console.log('✅ RLS policies: Working');

  } catch (error) {
    console.error('❌ Prisma connection test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Disconnected from database');
  }
}

testPrismaConnection();
