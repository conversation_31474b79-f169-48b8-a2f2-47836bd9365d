const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testCRUDOperations() {
  console.log('🧪 Testing comprehensive CRUD operations...');
  
  const testData = {
    userId: uuidv4(),
    phoneNumberId: uuidv4(),
    callId: uuidv4(),
    messageId: uuidv4(),
    automationId: uuidv4(),
    aiAgentId: uuidv4()
  };

  try {
    // 1. CREATE Operations
    console.log('\n1️⃣ Testing CREATE operations...');
    
    // Create test user
    const { data: user, error: userError } = await supabase
      .from('users')
      .insert({
        id: testData.userId,
        email: '<EMAIL>',
        name: 'CRUD Test User',
        timezone: 'UTC',
        preferences: { theme: 'dark', notifications: true }
      })
      .select()
      .single();

    if (userError) throw new Error(`User creation failed: ${userError.message}`);
    console.log('✅ User created:', user.email);

    // Create test phone number
    const { data: phoneNumber, error: phoneError } = await supabase
      .from('phone_numbers')
      .insert({
        id: testData.phoneNumberId,
        user_id: testData.userId,
        number: '+1234567890',
        friendly_name: 'Test Phone Number',
        country_code: 'US',
        twilio_sid: 'test-sid-' + Date.now(),
        is_active: true,
        is_primary: false,
        capabilities: { voice: true, sms: true }
      })
      .select()
      .single();

    if (phoneError) throw new Error(`Phone number creation failed: ${phoneError.message}`);
    console.log('✅ Phone number created:', phoneNumber.number);

    // Create test call
    const { data: call, error: callError } = await supabase
      .from('calls')
      .insert({
        id: testData.callId,
        user_id: testData.userId,
        phone_number_id: testData.phoneNumberId,
        external_id: 'call-sid-' + Date.now(),
        from_number: '+0987654321',
        to_number: '+1234567890',
        direction: 'inbound',
        status: 'completed',
        duration_seconds: 120,
        ai_analysis: { sentiment: 'positive', summary: 'Test call summary' }
      })
      .select()
      .single();

    if (callError) throw new Error(`Call creation failed: ${callError.message}`);
    console.log('✅ Call created:', call.external_id);

    // Create test message
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        id: testData.messageId,
        user_id: testData.userId,
        phone_number_id: testData.phoneNumberId,
        external_id: 'msg-sid-' + Date.now(),
        from_number: '+0987654321',
        to_number: '+1234567890',
        direction: 'inbound',
        body: 'Test message body',
        status: 'delivered',
        ai_response: { generated: true, response: 'Auto-reply test' }
      })
      .select()
      .single();

    if (messageError) throw new Error(`Message creation failed: ${messageError.message}`);
    console.log('✅ Message created:', message.body);

    // Create test AI agent
    const { data: aiAgent, error: agentError } = await supabase
      .from('ai_agents')
      .insert({
        id: testData.aiAgentId,
        user_id: testData.userId,
        name: 'Test AI Agent',
        description: 'CRUD test agent',
        prompt: 'You are a helpful AI assistant for testing CRUD operations.',
        voice_id: 'test-voice-id',
        voice_settings: { voice_id: 'test-voice', speed: 1.0 },
        llm_config: { model: 'gpt-4', temperature: 0.7 },
        conversation_config: { max_turns: 10, timeout: 30 },
        knowledge_base: { documents: [], training_status: 'ready' },
        is_active: true
      })
      .select()
      .single();

    if (agentError) throw new Error(`AI Agent creation failed: ${agentError.message}`);
    console.log('✅ AI Agent created:', aiAgent.name);

    // 2. READ Operations
    console.log('\n2️⃣ Testing READ operations...');
    
    // Read user with related data
    const { data: userData, error: readUserError } = await supabase
      .from('users')
      .select(`
        *,
        phone_numbers(*),
        calls(count),
        messages(count)
      `)
      .eq('id', testData.userId)
      .single();

    if (readUserError) throw new Error(`User read failed: ${readUserError.message}`);
    console.log('✅ User read with relations:', {
      name: userData.name,
      phoneNumbers: userData.phone_numbers.length,
      preferences: userData.preferences
    });

    // Read with filtering and pagination
    const { data: recentCalls, error: callsError } = await supabase
      .from('calls')
      .select('*')
      .eq('user_id', testData.userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (callsError) throw new Error(`Calls read failed: ${callsError.message}`);
    console.log('✅ Recent calls read:', recentCalls.length);

    // 3. UPDATE Operations
    console.log('\n3️⃣ Testing UPDATE operations...');
    
    // Update user preferences
    const { data: updatedUser, error: updateUserError } = await supabase
      .from('users')
      .update({
        preferences: { theme: 'light', notifications: false, updated: true }
      })
      .eq('id', testData.userId)
      .select()
      .single();

    if (updateUserError) throw new Error(`User update failed: ${updateUserError.message}`);
    console.log('✅ User updated:', updatedUser.preferences);

    // Update call with AI analysis
    const { data: updatedCall, error: updateCallError } = await supabase
      .from('calls')
      .update({
        ai_analysis: { 
          sentiment: 'neutral', 
          summary: 'Updated test call summary',
          keywords: ['test', 'crud', 'update']
        }
      })
      .eq('id', testData.callId)
      .select()
      .single();

    if (updateCallError) throw new Error(`Call update failed: ${updateCallError.message}`);
    console.log('✅ Call updated with AI analysis');

    // 4. Complex Queries
    console.log('\n4️⃣ Testing complex queries...');
    
    // Aggregate query
    const { data: stats, error: statsError } = await supabase
      .rpc('get_user_stats', { user_id_param: testData.userId });

    // If RPC doesn't exist, use regular aggregation
    if (statsError) {
      const { count: callCount } = await supabase
        .from('calls')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', testData.userId);
      
      const { count: messageCount } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', testData.userId);
      
      console.log('✅ User statistics:', { calls: callCount, messages: messageCount });
    } else {
      console.log('✅ User statistics (RPC):', stats);
    }

    // 5. DELETE Operations (Cleanup)
    console.log('\n5️⃣ Testing DELETE operations (cleanup)...');
    
    // Delete in correct order (respecting foreign keys)
    const deleteOperations = [
      { table: 'calls', id: testData.callId },
      { table: 'messages', id: testData.messageId },
      { table: 'ai_agents', id: testData.aiAgentId },
      { table: 'phone_numbers', id: testData.phoneNumberId },
      { table: 'users', id: testData.userId }
    ];

    for (const op of deleteOperations) {
      const { error: deleteError } = await supabase
        .from(op.table)
        .delete()
        .eq('id', op.id);
      
      if (deleteError) {
        console.warn(`⚠️ ${op.table} deletion warning:`, deleteError.message);
      } else {
        console.log(`✅ ${op.table} deleted successfully`);
      }
    }

    console.log('\n🎉 All CRUD operations completed successfully!');
    console.log('✅ CREATE: All entities created');
    console.log('✅ READ: Data retrieval and relations working');
    console.log('✅ UPDATE: Data modifications successful');
    console.log('✅ DELETE: Cleanup operations completed');
    console.log('✅ JSONB: Complex data types working');
    console.log('✅ Relations: Foreign key relationships maintained');

  } catch (error) {
    console.error('❌ CRUD operations test failed:', error.message);
    console.error('Full error:', error);
    
    // Attempt cleanup on failure
    console.log('\n🧹 Attempting cleanup after failure...');
    const cleanupIds = Object.values(testData);
    const tables = ['calls', 'messages', 'ai_agents', 'phone_numbers', 'users'];
    
    for (const table of tables) {
      for (const id of cleanupIds) {
        await supabase.from(table).delete().eq('id', id);
      }
    }
  }
}

// Add UUID package check
async function checkDependencies() {
  try {
    require('uuid');
    console.log('✅ UUID package available');
  } catch (error) {
    console.log('⚠️ Installing UUID package...');
    const { exec } = require('child_process');
    exec('npm install uuid', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Failed to install UUID package:', error);
        return;
      }
      console.log('✅ UUID package installed');
    });
  }
}

checkDependencies().then(() => {
  testCRUDOperations();
});
