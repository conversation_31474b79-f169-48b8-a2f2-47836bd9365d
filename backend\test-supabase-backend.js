const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection from backend...');
  console.log('URL:', supabaseUrl);
  console.log('Service Key:', supabaseServiceKey ? 'Present' : 'Missing');
  
  try {
    // Test basic connection
    console.log('1. Testing database connection...');
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return;
    }
    
    console.log('✅ Successfully connected to database');
    console.log(`✅ Users table accessible - Count: ${data}`);

    // Test other tables
    const tables = ['phone_numbers', 'calls', 'messages', 'automations'];
    for (const table of tables) {
      const { data: tableData, error: tableError } = await supabase
        .from(table)
        .select('count', { count: 'exact', head: true });
      
      if (tableError) {
        console.error(`❌ ${table} table error:`, tableError.message);
      } else {
        console.log(`✅ ${table} table accessible - Count: ${tableData}`);
      }
    }

    // Test creating a test user
    console.log('2. Testing CRUD operations...');
    const testUserId = 'test-user-' + Date.now();
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        id: testUserId,
        email: '<EMAIL>',
        name: 'Test User',
        timezone: 'UTC',
        preferences: {}
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ User creation failed:', createError.message);
    } else {
      console.log('✅ User creation successful:', newUser.email);
      
      // Clean up test user
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', testUserId);
      
      if (deleteError) {
        console.error('❌ User deletion failed:', deleteError.message);
      } else {
        console.log('✅ User deletion successful');
      }
    }

    console.log('\n🎉 All Supabase backend connection tests passed!');

  } catch (error) {
    console.error('❌ Supabase connection test failed:', error.message);
    console.error('Full error:', error);
  }
}

testSupabaseConnection();
